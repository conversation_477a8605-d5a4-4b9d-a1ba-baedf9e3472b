"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.EcsFargateAndSpotStack = void 0;
const cdk = require("aws-cdk-lib");
const ec2 = require("aws-cdk-lib/aws-ec2");
const ecs = require("aws-cdk-lib/aws-ecs");
const ecsPatterns = require("aws-cdk-lib/aws-ecs-patterns");
const logs = require("aws-cdk-lib/aws-logs");
const iam = require("aws-cdk-lib/aws-iam");
const ecr = require("aws-cdk-lib/aws-ecr");
const apigatewayv2 = require("aws-cdk-lib/aws-apigatewayv2");
const apigatewayv2Integrations = require("aws-cdk-lib/aws-apigatewayv2-integrations");
const elasticloadbalancingv2 = require("aws-cdk-lib/aws-elasticloadbalancingv2");
/**
 * ECS Fargate and Spot Stack
 *
 * Creates a comprehensive container deployment using AWS ECS Fargate
 * Combines both Fargate and Spot capacity strategies for cost optimization
 * Includes networking, load balancing, auto-scaling, and monitoring
 *
 * Features:
 * - Cost-optimized VPC with public subnets only (no NAT Gateway costs)
 * - ECS cluster with mixed capacity providers (Fargate + Spot)
 * - ECS tasks run in public subnets with public IPs
 * - HTTP API Gateway as front door for microservices (70% cheaper, 2x faster)
 * - Network Load Balancer for high performance and lower latency
 * - VPC Link for secure API Gateway to NLB connectivity
 * - Auto-scaling based on CPU and memory utilization
 * - CloudWatch logging and monitoring
 * - ECR repository for container images
 * - Security groups optimized for public subnet deployment
 */
class EcsFargateAndSpotStack extends cdk.Stack {
    constructor(scope, id, props) {
        super(scope, id, props);
        /**
         * VPC Configuration - Cost Optimized
         * Creates a VPC with only public subnets across multiple AZs
         * No NAT Gateways needed - ECS tasks run in public subnets with public IPs
         * Significant cost savings: ~$90/month savings (2 NAT Gateways eliminated)
         */
        const vpc = new ec2.Vpc(this, "ExpressFargateVpc", {
            maxAzs: 3,
            cidr: "10.0.0.0/16",
            natGateways: 0,
            subnetConfiguration: [
                {
                    cidrMask: 24,
                    name: "Public",
                    subnetType: ec2.SubnetType.PUBLIC,
                },
            ],
            enableDnsHostnames: true,
            enableDnsSupport: true,
        });
        // Tag VPC resources for better organization and cost tracking
        cdk.Tags.of(vpc).add("Project", "ExpressFargateApp");
        cdk.Tags.of(vpc).add("Environment", props?.tags?.Environment || "Development");
        /**
         * ECR Repository
         * Use existing repository or create a new one
         * Configured with lifecycle policies for cost optimization
         */
        this.repository = ecr.Repository.fromRepositoryName(this, "ExpressAppRepository", "express-fargate-app");
        /**
         * CloudWatch Log Group
         * Centralized logging for all ECS tasks with retention policy
         */
        const logGroup = new logs.LogGroup(this, "ExpressAppLogGroup", {
            logGroupName: "/ecs/express-fargate-app",
            retention: logs.RetentionDays.ONE_WEEK,
            removalPolicy: cdk.RemovalPolicy.DESTROY, // For development
        });
        /**
         * ECS Cluster Configuration
         * Creates an ECS cluster with mixed capacity providers
         * Supports both Fargate and Fargate Spot for cost optimization
         */
        this.cluster = new ecs.Cluster(this, "ExpressFargateCluster", {
            clusterName: "express-fargate-cluster",
            vpc: vpc,
            containerInsights: true, // Enable CloudWatch Container Insights
        });
        /**
         * Capacity Providers Configuration
         * Defines how tasks are distributed between Fargate and Spot
         * Fargate: Reliable, on-demand capacity
         * Fargate Spot: Cost-optimized, interruptible capacity
         */
        // Enable Fargate and Fargate Spot capacity providers
        const cfnCluster = this.cluster.node.defaultChild;
        cfnCluster.capacityProviders = ["FARGATE", "FARGATE_SPOT"];
        cfnCluster.defaultCapacityProviderStrategy = [
            {
                capacityProvider: "FARGATE",
                weight: 1,
                base: 1, // At least 1 task on Fargate for reliability
            },
            {
                capacityProvider: "FARGATE_SPOT",
                weight: 4, // 80% of tasks on Fargate Spot for cost savings
            },
        ];
        /**
         * Task Definition
         * Defines the container configuration and resource requirements
         * Includes proper logging, environment variables, and health checks
         */
        const taskDefinition = new ecs.FargateTaskDefinition(this, "ExpressAppTaskDefinition", {
            memoryLimitMiB: 1024,
            cpu: 512,
            family: "express-fargate-app",
        });
        // Task execution role for ECR and CloudWatch access
        taskDefinition.executionRole?.addManagedPolicy(iam.ManagedPolicy.fromAwsManagedPolicyName("service-role/AmazonECSTaskExecutionRolePolicy"));
        // Additional permissions for CloudWatch and ECR
        if (taskDefinition.executionRole &&
            "addToPolicy" in taskDefinition.executionRole) {
            taskDefinition.executionRole.addToPolicy(new iam.PolicyStatement({
                effect: iam.Effect.ALLOW,
                actions: [
                    "ecr:GetAuthorizationToken",
                    "ecr:BatchCheckLayerAvailability",
                    "ecr:GetDownloadUrlForLayer",
                    "ecr:BatchGetImage",
                    "logs:CreateLogStream",
                    "logs:PutLogEvents",
                ],
                resources: ["*"],
            }));
        }
        /**
         * Container Definition
         * Configures the Express application container with proper settings
         */
        const container = taskDefinition.addContainer("ExpressAppContainer", {
            image: ecs.ContainerImage.fromEcrRepository(this.repository, "latest"),
            containerName: "express-app",
            environment: {
                NODE_ENV: "production",
                PORT: "3000",
                APP_VERSION: "1.0.0",
                LOG_LEVEL: "info",
            },
            logging: ecs.LogDrivers.awsLogs({
                streamPrefix: "express-app",
                logGroup: logGroup,
            }),
            healthCheck: {
                command: [
                    "CMD-SHELL",
                    "curl -f http://localhost:3000/health || exit 1",
                ],
                interval: cdk.Duration.seconds(30),
                timeout: cdk.Duration.seconds(5),
                retries: 3,
                startPeriod: cdk.Duration.seconds(60),
            },
        });
        // Expose container port
        container.addPortMappings({
            containerPort: 3000,
            protocol: ecs.Protocol.TCP,
        });
        /**
         * Security Group for ECS Tasks - Public Subnet Configuration
         * Allows inbound traffic from the load balancer and internet access
         * Tasks run in public subnets with public IPs for cost optimization
         */
        const ecsSecurityGroup = new ec2.SecurityGroup(this, "EcsTaskSecurityGroup", {
            vpc: vpc,
            description: "Security group for ECS Fargate tasks in public subnets",
            allowAllOutbound: true, // Allow outbound for API calls and dependencies
        });
        /**
         * Network Load Balancer Security Group
         * NLB operates at Layer 4 and doesn't need security groups for itself
         * Security is handled at the target (ECS task) level
         */
        // Allow NLB to communicate with ECS tasks
        ecsSecurityGroup.addIngressRule(ec2.Peer.anyIpv4(), ec2.Port.tcp(3000), "Allow traffic from NLB to ECS tasks");
        // Optional: Allow direct access to ECS tasks for debugging (remove in production)
        // ecsSecurityGroup.addIngressRule(
        //   ec2.Peer.anyIpv4(),
        //   ec2.Port.tcp(8080),
        //   "Allow direct access to ECS tasks (debugging only)"
        // );
        /**
         * Fargate Service with Network Load Balancer - High Performance
         * Tasks run in public subnets with public IPs (no NAT Gateway needed)
         * NLB provides higher performance and lower latency than ALB
         */
        const fargateService = new ecsPatterns.NetworkLoadBalancedFargateService(this, "ExpressFargateService", {
            cluster: this.cluster,
            taskDefinition: taskDefinition,
            serviceName: "express-fargate-service",
            publicLoadBalancer: true,
            desiredCount: 2,
            listenerPort: 80,
            domainZone: undefined,
            domainName: undefined,
            platformVersion: ecs.FargatePlatformVersion.LATEST,
            assignPublicIp: true,
            taskSubnets: {
                subnetType: ec2.SubnetType.PUBLIC, // Explicitly use public subnets
            },
        });
        // Configure health check for the target group (TCP health checks for NLB)
        fargateService.targetGroup.configureHealthCheck({
            protocol: elasticloadbalancingv2.Protocol.TCP,
            port: "3000",
            interval: cdk.Duration.seconds(30),
            timeout: cdk.Duration.seconds(10),
            healthyThresholdCount: 2,
            unhealthyThresholdCount: 3,
        });
        // Set deregistration delay for faster deployments
        fargateService.targetGroup.setAttribute("deregistration_delay.timeout_seconds", "30");
        this.service = fargateService.service;
        this.networkLoadBalancer = fargateService.loadBalancer;
        /**
         * VPC Link for API Gateway to NLB
         * Enables secure connectivity from API Gateway to private resources
         */
        const vpcLink = new apigatewayv2.VpcLink(this, "ExpressAppVpcLink", {
            vpc: vpc,
            subnets: { subnetType: ec2.SubnetType.PUBLIC },
        });
        /**
         * HTTP API Gateway Configuration - Optimized for Microservices
         * Provides 70% cost savings and 2x better performance vs REST API
         * Perfect for microservices architecture with simple proxy needs
         */
        this.httpApi = new apigatewayv2.HttpApi(this, "ExpressAppApi", {
            apiName: "Express Fargate API",
            description: "HTTP API Gateway for Express application on ECS Fargate",
            // CORS configuration for HTTP API
            corsPreflight: {
                allowOrigins: ["*"],
                allowMethods: [
                    apigatewayv2.CorsHttpMethod.GET,
                    apigatewayv2.CorsHttpMethod.POST,
                    apigatewayv2.CorsHttpMethod.PUT,
                    apigatewayv2.CorsHttpMethod.DELETE,
                    apigatewayv2.CorsHttpMethod.HEAD,
                    apigatewayv2.CorsHttpMethod.OPTIONS,
                    apigatewayv2.CorsHttpMethod.PATCH,
                ],
                allowHeaders: [
                    "Content-Type",
                    "X-Amz-Date",
                    "Authorization",
                    "X-Api-Key",
                    "X-Amz-Security-Token",
                ],
            },
        });
        /**
         * HTTP API Gateway Integration with NLB via VPC Link
         * Routes all requests to the Network Load Balancer
         */
        const nlbIntegration = new apigatewayv2Integrations.HttpNlbIntegration("NlbIntegration", fargateService.listener, {
            vpcLink: vpcLink,
        });
        /**
         * HTTP API Gateway Routes
         * Proxy all requests to the backend service
         */
        this.httpApi.addRoutes({
            path: "/{proxy+}",
            methods: [apigatewayv2.HttpMethod.ANY],
            integration: nlbIntegration,
        });
        // Add root path route
        this.httpApi.addRoutes({
            path: "/",
            methods: [apigatewayv2.HttpMethod.ANY],
            integration: nlbIntegration,
        });
        /**
         * HTTP API Gateway Throttling Configuration
         * HTTP API uses different throttling configuration than REST API
         */
        const stage = this.httpApi.addStage("prod", {
            stageName: "prod",
            throttle: {
                burstLimit: 2000,
                rateLimit: 1000, // requests per second
            },
        });
        // Enable logging for the HTTP API stage
        stage.node.addDependency(this.httpApi);
        /**
         * Auto Scaling Configuration
         * Scales the service based on CPU and memory utilization
         * Ensures optimal resource usage and cost efficiency
         */
        const scalableTarget = this.service.autoScaleTaskCount({
            minCapacity: 2,
            maxCapacity: 20, // Maximum tasks to prevent excessive costs
        });
        // CPU-based scaling
        scalableTarget.scaleOnCpuUtilization("CpuScaling", {
            targetUtilizationPercent: 70,
            scaleInCooldown: cdk.Duration.minutes(5),
            scaleOutCooldown: cdk.Duration.minutes(2),
        });
        // Memory-based scaling
        scalableTarget.scaleOnMemoryUtilization("MemoryScaling", {
            targetUtilizationPercent: 80,
            scaleInCooldown: cdk.Duration.minutes(5),
            scaleOutCooldown: cdk.Duration.minutes(2),
        });
        /**
         * CloudFormation Outputs
         * Provides important resource information for external access
         */
        new cdk.CfnOutput(this, "ApiGatewayURL", {
            value: this.httpApi.url ||
                `https://${this.httpApi.httpApiId}.execute-api.${this.region}.amazonaws.com/`,
            description: "HTTP API Gateway URL (primary endpoint)",
            exportName: "ExpressApp-ApiGatewayURL",
        });
        new cdk.CfnOutput(this, "NetworkLoadBalancerDNS", {
            value: this.networkLoadBalancer.loadBalancerDnsName,
            description: "Network Load Balancer DNS name",
            exportName: "ExpressApp-NetworkLoadBalancerDNS",
        });
        new cdk.CfnOutput(this, "ECRRepositoryURI", {
            value: this.repository.repositoryUri,
            description: "ECR Repository URI for container images",
            exportName: "ExpressApp-ECRRepositoryURI",
        });
        new cdk.CfnOutput(this, "ClusterName", {
            value: this.cluster.clusterName,
            description: "ECS Cluster name",
            exportName: "ExpressApp-ClusterName",
        });
        new cdk.CfnOutput(this, "ServiceName", {
            value: this.service.serviceName,
            description: "ECS Service name",
            exportName: "ExpressApp-ServiceName",
        });
        new cdk.CfnOutput(this, "HealthCheckUrl", {
            value: `${this.httpApi.url ||
                `https://${this.httpApi.httpApiId}.execute-api.${this.region}.amazonaws.com/`}health`,
            description: "Application health check URL via HTTP API Gateway",
            exportName: "ExpressApp-HealthCheckUrl",
        });
        new cdk.CfnOutput(this, "VpcLinkId", {
            value: vpcLink.vpcLinkId,
            description: "VPC Link ID for API Gateway to NLB connectivity",
            exportName: "ExpressApp-VpcLinkId",
        });
        /**
         * Resource Tagging
         * Apply consistent tags for resource management and cost tracking
         */
        const commonTags = {
            Project: "ExpressFargateApp",
            Environment: props?.tags?.Environment || "Development",
            Service: "ECS-Fargate",
            CostCenter: "Engineering",
            Owner: "DevOps-Team",
        };
        // Apply tags to all resources in the stack
        Object.entries(commonTags).forEach(([key, value]) => {
            cdk.Tags.of(this).add(key, value);
        });
    }
}
exports.EcsFargateAndSpotStack = EcsFargateAndSpotStack;
//# sourceMappingURL=data:application/json;base64,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