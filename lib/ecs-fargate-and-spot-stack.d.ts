import * as cdk from "aws-cdk-lib";
import { Construct } from "constructs";
import * as ecs from "aws-cdk-lib/aws-ecs";
import * as ecr from "aws-cdk-lib/aws-ecr";
import * as apigatewayv2 from "aws-cdk-lib/aws-apigatewayv2";
import * as elasticloadbalancingv2 from "aws-cdk-lib/aws-elasticloadbalancingv2";
/**
 * ECS Fargate and Spot Stack
 *
 * Creates a comprehensive container deployment using AWS ECS Fargate
 * Combines both Fargate and Spot capacity strategies for cost optimization
 * Includes networking, load balancing, auto-scaling, and monitoring
 *
 * Features:
 * - Cost-optimized VPC with public subnets only (no NAT Gateway costs)
 * - ECS cluster with mixed capacity providers (Fargate + Spot)
 * - ECS tasks run in public subnets with public IPs
 * - HTTP API Gateway as front door for microservices (70% cheaper, 2x faster)
 * - Network Load Balancer for high performance and lower latency
 * - VPC Link for secure API Gateway to NLB connectivity
 * - Auto-scaling based on CPU and memory utilization
 * - CloudWatch logging and monitoring
 * - ECR repository for container images
 * - Security groups optimized for public subnet deployment
 */
export declare class EcsFargateAndSpotStack extends cdk.Stack {
    /**
     * Public properties for cross-stack references
     */
    readonly cluster: ecs.Cluster;
    readonly service: ecs.FargateService;
    readonly networkLoadBalancer: elasticloadbalancingv2.NetworkLoadBalancer;
    readonly httpApi: apigatewayv2.HttpApi;
    readonly repository: ecr.IRepository;
    constructor(scope: Construct, id: string, props?: cdk.StackProps);
}
